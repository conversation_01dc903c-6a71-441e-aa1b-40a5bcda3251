<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DrissionPage 完整API思维导图</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }

        .search-box {
            flex: 1;
            min-width: 300px;
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: 12px 45px 12px 15px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
        }

        .search-icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        .mindmap-container {
            padding: 30px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .mindmap {
            font-size: 14px;
            line-height: 1.6;
        }

        .node {
            margin: 8px 0;
            position: relative;
        }

        .node-content {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .node-content:hover {
            background: #f8f9fa;
            transform: translateX(5px);
        }

        .toggle {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #3498db;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toggle:hover {
            background: #2980b9;
            transform: scale(1.1);
        }

        .toggle.collapsed::before {
            content: '+';
        }

        .toggle.expanded::before {
            content: '−';
        }

        .node-title {
            font-weight: 600;
            color: #2c3e50;
            margin-right: 10px;
        }

        .node-description {
            color: #7f8c8d;
            font-size: 13px;
        }

        .children {
            margin-left: 30px;
            border-left: 2px solid #ecf0f1;
            padding-left: 15px;
            display: none;
        }

        .children.expanded {
            display: block;
        }

        /* 不同层级的样式 */
        .level-0 .node-content {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            font-size: 18px;
            font-weight: bold;
            border-left-color: #c0392b;
        }

        .level-1 .node-content {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            font-size: 16px;
            font-weight: 600;
            border-left-color: #2980b9;
        }

        .level-2 .node-content {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            font-size: 15px;
            border-left-color: #27ae60;
        }

        .level-3 .node-content {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
            border-left-color: #e67e22;
        }

        .level-4 .node-content {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
            color: white;
            border-left-color: #8e44ad;
        }

        .level-5 .node-content {
            background: #ecf0f1;
            color: #2c3e50;
            border-left-color: #bdc3c7;
        }

        .code-example {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            overflow-x: auto;
            display: none;
        }

        .code-example.show {
            display: block;
        }

        .api-tag {
            background: #e74c3c;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            margin-left: 8px;
        }

        .api-tag.method { background: #3498db; }
        .api-tag.property { background: #2ecc71; }
        .api-tag.class { background: #f39c12; }
        .api-tag.event { background: #9b59b6; }

        .highlight {
            background: #fff3cd !important;
            border-left-color: #ffc107 !important;
        }

        .stats {
            display: flex;
            gap: 20px;
            margin-top: 15px;
        }

        .stat-item {
            background: rgba(255,255,255,0.2);
            padding: 10px 15px;
            border-radius: 20px;
            color: white;
            font-size: 14px;
        }

        /* 视图切换样式 */
        .view-toggle {
            display: flex;
            background: #f8f9fa;
            border-radius: 25px;
            padding: 4px;
            margin-right: 15px;
        }

        .view-toggle button {
            padding: 8px 16px;
            border: none;
            background: transparent;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .view-toggle button.active {
            background: #3498db;
            color: white;
        }

        .view-toggle button:hover:not(.active) {
            background: #e9ecef;
        }

        /* 可视化图形样式 */
        .visual-mindmap {
            display: none;
            width: 100%;
            height: 80vh;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .visual-mindmap.active {
            display: block;
        }

        .visual-mindmap svg {
            width: 100%;
            height: 100%;
            cursor: grab;
        }

        .visual-mindmap svg:active {
            cursor: grabbing;
        }

        /* 节点样式 */
        .node-rect {
            stroke: #fff;
            stroke-width: 2px;
            cursor: pointer;
            transition: all 0.3s ease;
            rx: 8;
            ry: 8;
        }

        .node-rect:hover {
            stroke-width: 3px;
            filter: brightness(1.1);
        }

        .node-text {
            font-family: 'Segoe UI', sans-serif;
            font-size: 12px;
            text-anchor: middle;
            pointer-events: none;
            fill: #fff;
            font-weight: 600;
            dominant-baseline: central;
        }

        .node-text.dark {
            fill: #2c3e50;
        }

        .link {
            fill: none;
            stroke: #bdc3c7;
            stroke-width: 2px;
            stroke-opacity: 0.6;
        }

        .link:hover {
            stroke: #3498db;
            stroke-width: 3px;
            stroke-opacity: 1;
        }

        /* 详细信息面板 */
        .info-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 300px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 20px;
            display: none;
            z-index: 1000;
            max-height: 400px;
            overflow-y: auto;
        }

        .info-panel.show {
            display: block;
        }

        .info-panel h3 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 16px;
        }

        .info-panel p {
            margin: 5px 0;
            color: #7f8c8d;
            font-size: 14px;
            line-height: 1.4;
        }

        .info-panel .code-block {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }

        .info-panel .close-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: none;
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: #7f8c8d;
        }

        .info-panel .close-btn:hover {
            color: #2c3e50;
        }

        /* 控制面板 */
        .graph-controls {
            position: absolute;
            top: 20px;
            left: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .graph-controls button {
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            background: #3498db;
            color: white;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .graph-controls button:hover {
            background: #2980b9;
        }

        /* 图例 */
        .legend {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(255,255,255,0.9);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            font-size: 12px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin: 5px 0;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 8px;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                min-width: auto;
            }

            .mindmap-container {
                padding: 15px;
            }

            .children {
                margin-left: 15px;
                padding-left: 10px;
            }

            .info-panel {
                width: 250px;
                right: 10px;
                top: 10px;
            }

            .graph-controls {
                left: 10px;
                top: 10px;
            }

            .legend {
                left: 10px;
                bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 DrissionPage 完整API思维导图</h1>
            <p>基于deepwiki和context7工具的全面API文档整理</p>
            <div class="stats">
                <div class="stat-item">📚 8大核心模块</div>
                <div class="stat-item">🔧 200+ API方法</div>
                <div class="stat-item">💡 386个代码示例</div>
                <div class="stat-item">🎯 完整功能覆盖</div>
            </div>
        </div>

        <div class="controls">
            <div class="view-toggle">
                <button id="listViewBtn" class="active" onclick="switchView('list')">📋 列表视图</button>
                <button id="graphViewBtn" onclick="switchView('graph')">🌐 图形视图</button>
            </div>
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="搜索API方法、类名或功能...">
                <span class="search-icon">🔍</span>
            </div>
            <button class="btn btn-primary" onclick="expandAll()">展开全部</button>
            <button class="btn btn-secondary" onclick="collapseAll()">收起全部</button>
            <button class="btn btn-primary" onclick="showCodeExamples()">显示代码示例</button>
        </div>

        <div class="mindmap-container">
            <div class="mindmap" id="mindmap">
                <!-- 思维导图内容将通过JavaScript生成 -->
            </div>

            <!-- 可视化思维导图 -->
            <div class="visual-mindmap" id="visualMindmap">
                <div class="graph-controls">
                    <button onclick="resetZoom()">🔍 重置缩放</button>
                    <button onclick="fitToScreen()">📐 适应屏幕</button>
                    <button onclick="expandAllNodes()">➕ 展开全部</button>
                    <button onclick="collapseAllNodes()">➖ 收起全部</button>
                </div>

                <div class="info-panel" id="infoPanel">
                    <button class="close-btn" onclick="closeInfoPanel()">×</button>
                    <h3 id="infoTitle">节点信息</h3>
                    <p id="infoDescription"></p>
                    <div id="infoCode" class="code-block"></div>
                </div>

                <div class="legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #e74c3c; border-radius: 4px;"></div>
                        <span>根节点</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #3498db; border-radius: 4px;"></div>
                        <span>分类</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #2ecc71; border-radius: 4px;"></div>
                        <span>模块</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #f39c12; border-radius: 4px;"></div>
                        <span>类</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #9b59b6; border-radius: 4px;"></div>
                        <span>方法/属性</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 思维导图数据结构
        const mindmapData = {
            title: "DrissionPage",
            description: "Python Web自动化库 - 浏览器控制与HTTP请求的完美结合",
            type: "root",
            children: [
                {
                    title: "核心架构",
                    description: "DrissionPage的基础架构和核心组件",
                    type: "category",
                    children: [
                        {
                            title: "页面对象层",
                            description: "不同模式的页面控制对象",
                            type: "module",
                            children: [
                                {
                                    title: "ChromiumPage",
                                    description: "浏览器控制模式 - 真实浏览器环境",
                                    type: "class",
                                    children: [
                                        {
                                            title: "导航方法",
                                            type: "group",
                                            children: [
                                                { title: "get(url, show_errmsg=True, retry=None, interval=None, timeout=None)", description: "访问网页，支持重试机制", type: "method", code: "page.get('https://example.com')" },
                                                { title: "post(url, data=None, json=None, **kwargs)", description: "发送POST请求", type: "method", code: "page.post('https://api.example.com', json={'key': 'value'})" },
                                                { title: "refresh()", description: "刷新当前页面", type: "method", code: "page.refresh()" },
                                                { title: "back(steps=1)", description: "后退指定步数", type: "method", code: "page.back(2)" },
                                                { title: "forward(steps=1)", description: "前进指定步数", type: "method", code: "page.forward()" },
                                                { title: "stop_loading()", description: "停止页面加载", type: "method", code: "page.stop_loading()" }
                                            ]
                                        },
                                        {
                                            title: "元素查找",
                                            type: "group",
                                            children: [
                                                { title: "ele(locator, index=1, timeout=None)", description: "查找单个元素", type: "method", code: "element = page.ele('#myid')" },
                                                { title: "eles(locator, timeout=None)", description: "查找多个元素", type: "method", code: "elements = page.eles('.class-name')" },
                                                { title: "s_ele(locator)", description: "查找静态元素（快速）", type: "method", code: "element = page.s_ele('tag:div')" },
                                                { title: "s_eles(locator)", description: "查找多个静态元素", type: "method", code: "elements = page.s_eles('tag:a')" }
                                            ]
                                        },
                                        {
                                            title: "等待机制",
                                            type: "group",
                                            children: [
                                                { title: "wait.load_start(timeout=None)", description: "等待页面开始加载", type: "method", code: "page.wait.load_start()" },
                                                { title: "wait.doc_loaded(timeout=None)", description: "等待文档加载完成", type: "method", code: "page.wait.doc_loaded()" },
                                                { title: "wait.ele_displayed(locator, timeout=None)", description: "等待元素显示", type: "method", code: "page.wait.ele_displayed('#loading')" },
                                                { title: "wait.ele_hidden(locator, timeout=None)", description: "等待元素隐藏", type: "method", code: "page.wait.ele_hidden('.modal')" },
                                                { title: "wait.url_change(text, exclude=False, timeout=None)", description: "等待URL变化", type: "method", code: "page.wait.url_change('success')" },
                                                { title: "wait.title_change(text, exclude=False, timeout=None)", description: "等待标题变化", type: "method", code: "page.wait.title_change('完成')" }
                                            ]
                                        },
                                        {
                                            title: "页面属性",
                                            type: "group",
                                            children: [
                                                { title: "title", description: "页面标题", type: "property", code: "print(page.title)" },
                                                { title: "html", description: "页面HTML源码", type: "property", code: "html_content = page.html" },
                                                { title: "url", description: "当前页面URL", type: "property", code: "current_url = page.url" },
                                                { title: "user_agent", description: "用户代理字符串", type: "property", code: "ua = page.user_agent" },
                                                { title: "cookies", description: "页面Cookie", type: "property", code: "cookies = page.cookies" },
                                                { title: "session_storage", description: "会话存储", type: "property", code: "storage = page.session_storage" },
                                                { title: "local_storage", description: "本地存储", type: "property", code: "storage = page.local_storage" }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    title: "SessionPage",
                                    description: "HTTP会话模式 - 轻量级请求",
                                    type: "class",
                                    children: [
                                        {
                                            title: "请求方法",
                                            type: "group",
                                            children: [
                                                { title: "get(url, show_errmsg=True, retry=None, interval=None, **kwargs)", description: "发送GET请求", type: "method", code: "response = page.get('https://api.example.com')" },
                                                { title: "post(url, data=None, json=None, files=None, **kwargs)", description: "发送POST请求", type: "method", code: "response = page.post(url, json=data)" },
                                                { title: "put(url, data=None, **kwargs)", description: "发送PUT请求", type: "method", code: "response = page.put(url, data=data)" },
                                                { title: "delete(url, **kwargs)", description: "发送DELETE请求", type: "method", code: "response = page.delete(url)" },
                                                { title: "head(url, **kwargs)", description: "发送HEAD请求", type: "method", code: "response = page.head(url)" },
                                                { title: "options(url, **kwargs)", description: "发送OPTIONS请求", type: "method", code: "response = page.options(url)" }
                                            ]
                                        },
                                        {
                                            title: "响应属性",
                                            type: "group",
                                            children: [
                                                { title: "response", description: "最后一次响应对象", type: "property", code: "resp = page.response" },
                                                { title: "json", description: "响应JSON数据", type: "property", code: "data = page.json" },
                                                { title: "html", description: "响应HTML内容", type: "property", code: "html = page.html" },
                                                { title: "text", description: "响应文本内容", type: "property", code: "text = page.text" },
                                                { title: "content", description: "响应二进制内容", type: "property", code: "content = page.content" },
                                                { title: "status_code", description: "HTTP状态码", type: "property", code: "code = page.status_code" },
                                                { title: "headers", description: "响应头", type: "property", code: "headers = page.headers" },
                                                { title: "encoding", description: "响应编码", type: "property", code: "encoding = page.encoding" }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    title: "WebPage",
                                    description: "混合模式 - 可在浏览器和会话间切换",
                                    type: "class",
                                    children: [
                                        {
                                            title: "模式切换",
                                            type: "group",
                                            children: [
                                                { title: "change_mode(mode='d', go=True, copy_cookies=True)", description: "切换工作模式", type: "method", code: "page.change_mode('s')  # 切换到会话模式" },
                                                { title: "mode", description: "当前工作模式", type: "property", code: "current_mode = page.mode" }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            title: "元素对象层",
                            description: "页面元素的封装对象",
                            type: "module",
                            children: [
                                {
                                    title: "ChromiumElement",
                                    description: "浏览器模式下的元素对象",
                                    type: "class",
                                    children: [
                                        {
                                            title: "查找方法",
                                            type: "group",
                                            children: [
                                                { title: "ele(locator, index=1, timeout=None)", description: "查找子元素", type: "method", code: "child = element.ele('.child')" },
                                                { title: "eles(locator, timeout=None)", description: "查找多个子元素", type: "method", code: "children = element.eles('tag:div')" },
                                                { title: "parent(level_or_locator=1)", description: "获取父元素", type: "method", code: "parent = element.parent()" },
                                                { title: "child(locator='', index=1, timeout=None)", description: "获取子元素", type: "method", code: "child = element.child()" },
                                                { title: "children(locator='', timeout=None)", description: "获取所有子元素", type: "method", code: "children = element.children()" },
                                                { title: "next(locator='', index=1, timeout=None)", description: "获取下一个兄弟元素", type: "method", code: "next_elem = element.next()" },
                                                { title: "prev(locator='', index=1, timeout=None)", description: "获取上一个兄弟元素", type: "method", code: "prev_elem = element.prev()" },
                                                { title: "siblings(locator='', timeout=None)", description: "获取所有兄弟元素", type: "method", code: "siblings = element.siblings()" }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    title: "元素操作API",
                    description: "元素交互、属性操作和状态检查",
                    type: "category",
                    children: [
                        {
                            title: "交互方法",
                            description: "鼠标、键盘和表单交互",
                            type: "module",
                            children: [
                                {
                                    title: "点击操作",
                                    type: "group",
                                    children: [
                                        { title: "click(by_js=False, timeout=None)", description: "点击元素", type: "method", code: "element.click()" },
                                        { title: "click.left()", description: "左键点击", type: "method", code: "element.click.left()" },
                                        { title: "click.right()", description: "右键点击", type: "method", code: "element.click.right()" },
                                        { title: "click.middle()", description: "中键点击", type: "method", code: "element.click.middle()" },
                                        { title: "click.multi(times=2)", description: "多次点击", type: "method", code: "element.click.multi(3)" },
                                        { title: "click.at(offset_x=None, offset_y=None)", description: "在指定位置点击", type: "method", code: "element.click.at(10, 20)" }
                                    ]
                                },
                                {
                                    title: "输入操作",
                                    type: "group",
                                    children: [
                                        { title: "input(vals, clear=True, by_js=False)", description: "输入文本", type: "method", code: "element.input('Hello World')" },
                                        { title: "clear(by_js=False)", description: "清空内容", type: "method", code: "element.clear()" },
                                        { title: "focus()", description: "聚焦元素", type: "method", code: "element.focus()" },
                                        { title: "type(text, interval=0.05)", description: "逐字符输入", type: "method", code: "element.type('slow typing')" }
                                    ]
                                },
                                {
                                    title: "选择操作",
                                    type: "group",
                                    children: [
                                        { title: "select.by_text(text)", description: "按文本选择选项", type: "method", code: "element.select.by_text('选项1')" },
                                        { title: "select.by_value(value)", description: "按值选择选项", type: "method", code: "element.select.by_value('option1')" },
                                        { title: "select.by_index(index)", description: "按索引选择选项", type: "method", code: "element.select.by_index(0)" },
                                        { title: "select.all()", description: "选择所有选项", type: "method", code: "element.select.all()" },
                                        { title: "select.clear()", description: "清空选择", type: "method", code: "element.select.clear()" },
                                        { title: "select.invert()", description: "反选", type: "method", code: "element.select.invert()" }
                                    ]
                                },
                                {
                                    title: "拖拽操作",
                                    type: "group",
                                    children: [
                                        { title: "drag_to(target, duration=0.5)", description: "拖拽到目标元素", type: "method", code: "element.drag_to(target_element)" },
                                        { title: "drag(offset_x, offset_y, duration=0.5)", description: "拖拽指定距离", type: "method", code: "element.drag(100, 50)" },
                                        { title: "hover()", description: "鼠标悬停", type: "method", code: "element.hover()" }
                                    ]
                                }
                            ]
                        },
                        {
                            title: "属性操作",
                            description: "获取和设置元素属性",
                            type: "module",
                            children: [
                                {
                                    title: "获取属性",
                                    type: "group",
                                    children: [
                                        { title: "attr(name)", description: "获取指定属性值", type: "method", code: "value = element.attr('href')" },
                                        { title: "attrs", description: "所有属性字典", type: "property", code: "all_attrs = element.attrs" },
                                        { title: "text", description: "元素文本内容", type: "property", code: "text = element.text" },
                                        { title: "raw_text", description: "原始文本（包含换行）", type: "property", code: "raw = element.raw_text" },
                                        { title: "inner_html", description: "内部HTML", type: "property", code: "html = element.inner_html" },
                                        { title: "outer_html", description: "外部HTML", type: "property", code: "html = element.outer_html" },
                                        { title: "tag", description: "标签名", type: "property", code: "tag_name = element.tag" },
                                        { title: "value", description: "表单元素值", type: "property", code: "val = element.value" },
                                        { title: "src", description: "图片或媒体源", type: "property", code: "src = element.src" },
                                        { title: "href", description: "链接地址", type: "property", code: "url = element.href" }
                                    ]
                                },
                                {
                                    title: "设置属性",
                                    type: "group",
                                    children: [
                                        { title: "set.attr(name, value)", description: "设置属性值", type: "method", code: "element.set.attr('class', 'new-class')" },
                                        { title: "set.property(name, value)", description: "设置属性", type: "method", code: "element.set.property('checked', True)" },
                                        { title: "set.style(name, value)", description: "设置样式", type: "method", code: "element.set.style('color', 'red')" },
                                        { title: "remove_attr(name)", description: "删除属性", type: "method", code: "element.remove_attr('disabled')" }
                                    ]
                                }
                            ]
                        },
                        {
                            title: "状态检查",
                            description: "元素状态和可见性检查",
                            type: "module",
                            children: [
                                {
                                    title: "可见性状态",
                                    type: "group",
                                    children: [
                                        { title: "states.is_displayed", description: "是否显示", type: "property", code: "visible = element.states.is_displayed" },
                                        { title: "states.is_hidden", description: "是否隐藏", type: "property", code: "hidden = element.states.is_hidden" },
                                        { title: "states.is_in_viewport", description: "是否在视口内", type: "property", code: "in_view = element.states.is_in_viewport" },
                                        { title: "states.is_whole_in_viewport", description: "是否完全在视口内", type: "property", code: "whole_in_view = element.states.is_whole_in_viewport" }
                                    ]
                                },
                                {
                                    title: "交互状态",
                                    type: "group",
                                    children: [
                                        { title: "states.is_enabled", description: "是否启用", type: "property", code: "enabled = element.states.is_enabled" },
                                        { title: "states.is_clickable", description: "是否可点击", type: "property", code: "clickable = element.states.is_clickable" },
                                        { title: "states.is_selected", description: "是否选中", type: "property", code: "selected = element.states.is_selected" },
                                        { title: "states.is_checked", description: "是否勾选", type: "property", code: "checked = element.states.is_checked" }
                                    ]
                                },
                                {
                                    title: "位置信息",
                                    type: "group",
                                    children: [
                                        { title: "rect.location", description: "元素位置", type: "property", code: "x, y = element.rect.location" },
                                        { title: "rect.size", description: "元素大小", type: "property", code: "w, h = element.rect.size" },
                                        { title: "rect.midpoint", description: "中心点坐标", type: "property", code: "cx, cy = element.rect.midpoint" },
                                        { title: "rect.click_point", description: "点击点坐标", type: "property", code: "px, py = element.rect.click_point" }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    title: "高级功能",
                    description: "网络监控、下载管理、JavaScript执行等高级特性",
                    type: "category",
                    children: [
                        {
                            title: "网络监控",
                            description: "监听和拦截网络请求响应",
                            type: "module",
                            children: [
                                {
                                    title: "监听控制",
                                    type: "group",
                                    children: [
                                        { title: "listen.start(targets='', method='', resource_type='')", description: "开始监听网络请求", type: "method", code: "page.listen.start('api/data')" },
                                        { title: "listen.stop()", description: "停止监听", type: "method", code: "page.listen.stop()" },
                                        { title: "listen.clear()", description: "清空监听缓存", type: "method", code: "page.listen.clear()" },
                                        { title: "listen.wait(timeout=None, count=1)", description: "等待指定数量的数据包", type: "method", code: "packets = page.listen.wait(count=3)" }
                                    ]
                                },
                                {
                                    title: "数据包操作",
                                    type: "group",
                                    children: [
                                        { title: "packet.url", description: "请求URL", type: "property", code: "url = packet.url" },
                                        { title: "packet.method", description: "请求方法", type: "property", code: "method = packet.method" },
                                        { title: "packet.request.headers", description: "请求头", type: "property", code: "headers = packet.request.headers" },
                                        { title: "packet.request.postData", description: "POST数据", type: "property", code: "data = packet.request.postData" },
                                        { title: "packet.response.body", description: "响应体", type: "property", code: "body = packet.response.body" },
                                        { title: "packet.response.headers", description: "响应头", type: "property", code: "headers = packet.response.headers" },
                                        { title: "packet.response.status", description: "状态码", type: "property", code: "status = packet.response.status" }
                                    ]
                                }
                            ]
                        },
                        {
                            title: "下载管理",
                            description: "文件下载和下载任务管理",
                            type: "module",
                            children: [
                                {
                                    title: "下载方法",
                                    type: "group",
                                    children: [
                                        { title: "download(file_url, goal_path=None, rename=None, file_exists='rename', show_msg=True)", description: "下载文件", type: "method", code: "page.download('http://example.com/file.pdf')" },
                                        { title: "element.save(path=None, name=None, as_bytes=None)", description: "保存元素（图片等）", type: "method", code: "img_element.save('image.jpg')" }
                                    ]
                                },
                                {
                                    title: "下载配置",
                                    type: "group",
                                    children: [
                                        { title: "set.download_path(path)", description: "设置下载路径", type: "method", code: "page.set.download_path('./downloads')" },
                                        { title: "set.download_file_name(name)", description: "设置下载文件名", type: "method", code: "page.set.download_file_name('custom_name.pdf')" },
                                        { title: "set.download_mode(mode)", description: "设置下载模式", type: "method", code: "page.set.download_mode('single')" }
                                    ]
                                },
                                {
                                    title: "下载等待",
                                    type: "group",
                                    children: [
                                        { title: "wait.download_begin(timeout=None)", description: "等待下载开始", type: "method", code: "page.wait.download_begin()" },
                                        { title: "wait.downloads_done(timeout=None)", description: "等待所有下载完成", type: "method", code: "page.wait.downloads_done()" }
                                    ]
                                },
                                {
                                    title: "下载任务",
                                    type: "group",
                                    children: [
                                        { title: "DownloadMission", description: "下载任务对象", type: "class", code: "mission = DownloadMission(page, url)" },
                                        { title: "mission.wait()", description: "等待任务完成", type: "method", code: "mission.wait()" },
                                        { title: "mission.cancel()", description: "取消下载", type: "method", code: "mission.cancel()" },
                                        { title: "mission.rate", description: "下载进度", type: "property", code: "progress = mission.rate" }
                                    ]
                                }
                            ]
                        },
                        {
                            title: "JavaScript执行",
                            description: "在页面中执行JavaScript代码",
                            type: "module",
                            children: [
                                {
                                    title: "执行方法",
                                    type: "group",
                                    children: [
                                        { title: "run_js(script, *args, as_expr=False, timeout=None)", description: "执行JavaScript代码", type: "method", code: "result = page.run_js('return document.title')" },
                                        { title: "run_js_loaded(script, *args, as_expr=False)", description: "页面加载后执行JS", type: "method", code: "page.run_js_loaded('console.log(\"loaded\")')" },
                                        { title: "run_async_js(script, *args, as_expr=False)", description: "异步执行JavaScript", type: "method", code: "page.run_async_js('setTimeout(() => {}, 1000)')" }
                                    ]
                                },
                                {
                                    title: "元素JS执行",
                                    type: "group",
                                    children: [
                                        { title: "element.run_js(script, *args, as_expr=False, timeout=None)", description: "在元素上执行JS", type: "method", code: "element.run_js('arguments[0].style.color = \"red\"')" },
                                        { title: "element.click(by_js=True)", description: "通过JS点击", type: "method", code: "element.click(by_js=True)" },
                                        { title: "element.input(text, by_js=True)", description: "通过JS输入", type: "method", code: "element.input('text', by_js=True)" }
                                    ]
                                }
                            ]
                        },
                        {
                            title: "截图录屏",
                            description: "页面和元素的截图录屏功能",
                            type: "module",
                            children: [
                                {
                                    title: "截图功能",
                                    type: "group",
                                    children: [
                                        { title: "get_screenshot(path=None, name=None, as_bytes=None, full_page=False)", description: "页面截图", type: "method", code: "page.get_screenshot('screenshot.png')" },
                                        { title: "element.get_screenshot(path=None, name=None, as_bytes=None)", description: "元素截图", type: "method", code: "element.get_screenshot('element.png')" }
                                    ]
                                },
                                {
                                    title: "录屏功能",
                                    type: "group",
                                    children: [
                                        { title: "screencast.start(save_path=None, video_fps=30, video_bit_rate=5000000)", description: "开始录屏", type: "method", code: "page.screencast.start('video.mp4')" },
                                        { title: "screencast.stop()", description: "停止录屏", type: "method", code: "page.screencast.stop()" },
                                        { title: "screencast.set_mode(video_fps=None, video_bit_rate=None)", description: "设置录制参数", type: "method", code: "page.screencast.set_mode(fps=60)" }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    title: "配置管理",
                    description: "浏览器和会话的配置选项",
                    type: "category",
                    children: [
                        {
                            title: "ChromiumOptions",
                            description: "浏览器配置选项",
                            type: "class",
                            children: [
                                {
                                    title: "基础配置",
                                    type: "group",
                                    children: [
                                        { title: "set_browser_path(path)", description: "设置浏览器路径", type: "method", code: "co.set_browser_path('/path/to/chrome')" },
                                        { title: "set_user_data_path(path)", description: "设置用户数据路径", type: "method", code: "co.set_user_data_path('./user_data')" },
                                        { title: "headless(on_off=True)", description: "无头模式", type: "method", code: "co.headless()" },
                                        { title: "incognito(on_off=True)", description: "隐身模式", type: "method", code: "co.incognito()" },
                                        { title: "no_imgs(on_off=True)", description: "禁用图片", type: "method", code: "co.no_imgs()" },
                                        { title: "mute(on_off=True)", description: "静音模式", type: "method", code: "co.mute()" }
                                    ]
                                },
                                {
                                    title: "高级配置",
                                    type: "group",
                                    children: [
                                        { title: "set_argument(arg)", description: "添加启动参数", type: "method", code: "co.set_argument('--disable-web-security')" },
                                        { title: "remove_argument(arg)", description: "移除启动参数", type: "method", code: "co.remove_argument('--headless')" },
                                        { title: "set_pref(arg, value)", description: "设置用户偏好", type: "method", code: "co.set_pref('profile.default_content_settings.popups', 0)" },
                                        { title: "remove_pref(arg)", description: "移除用户偏好", type: "method", code: "co.remove_pref('profile.managed_default_content_settings')" },
                                        { title: "set_proxy(proxy)", description: "设置代理", type: "method", code: "co.set_proxy('127.0.0.1:8080')" },
                                        { title: "set_user_agent(user_agent)", description: "设置用户代理", type: "method", code: "co.set_user_agent('Custom UA')" }
                                    ]
                                },
                                {
                                    title: "窗口配置",
                                    type: "group",
                                    children: [
                                        { title: "set_window_size(width, height)", description: "设置窗口大小", type: "method", code: "co.set_window_size(1920, 1080)" },
                                        { title: "maximize()", description: "最大化窗口", type: "method", code: "co.maximize()" },
                                        { title: "minimize()", description: "最小化窗口", type: "method", code: "co.minimize()" }
                                    ]
                                }
                            ]
                        },
                        {
                            title: "SessionOptions",
                            description: "会话配置选项",
                            type: "class",
                            children: [
                                {
                                    title: "请求配置",
                                    type: "group",
                                    children: [
                                        { title: "set_headers(headers)", description: "设置请求头", type: "method", code: "so.set_headers({'User-Agent': 'Custom'})" },
                                        { title: "set_header(name, value)", description: "设置单个请求头", type: "method", code: "so.set_header('Accept', 'application/json')" },
                                        { title: "remove_header(name)", description: "移除请求头", type: "method", code: "so.remove_header('User-Agent')" },
                                        { title: "set_user_agent(user_agent)", description: "设置用户代理", type: "method", code: "so.set_user_agent('Custom UA')" }
                                    ]
                                },
                                {
                                    title: "代理和认证",
                                    type: "group",
                                    children: [
                                        { title: "set_proxies(http=None, https=None)", description: "设置代理", type: "method", code: "so.set_proxies(http='127.0.0.1:8080')" },
                                        { title: "set_auth(auth)", description: "设置认证", type: "method", code: "so.set_auth(('user', 'pass'))" },
                                        { title: "set_cert(cert)", description: "设置客户端证书", type: "method", code: "so.set_cert('/path/to/cert.pem')" },
                                        { title: "set_verify(on_off)", description: "SSL验证", type: "method", code: "so.set_verify(False)" }
                                    ]
                                },
                                {
                                    title: "超时和重试",
                                    type: "group",
                                    children: [
                                        { title: "set_timeout(second)", description: "设置超时", type: "method", code: "so.set_timeout(30)" },
                                        { title: "set_retry(times=None, interval=None)", description: "设置重试", type: "method", code: "so.set_retry(3, 1)" },
                                        { title: "set_download_timeout(second)", description: "下载超时", type: "method", code: "so.set_download_timeout(60)" }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    title: "工具类和辅助",
                    description: "Actions、Keys、滚动等辅助工具",
                    type: "category",
                    children: [
                        {
                            title: "Actions类",
                            description: "鼠标键盘动作链",
                            type: "class",
                            children: [
                                {
                                    title: "鼠标操作",
                                    type: "group",
                                    children: [
                                        { title: "move_to(ele_or_loc, duration=0.5)", description: "移动鼠标", type: "method", code: "Actions(page).move_to(element)" },
                                        { title: "click(ele_or_loc=None)", description: "点击", type: "method", code: "Actions(page).click()" },
                                        { title: "r_click(ele_or_loc=None)", description: "右键点击", type: "method", code: "Actions(page).r_click()" },
                                        { title: "m_click(ele_or_loc=None)", description: "中键点击", type: "method", code: "Actions(page).m_click()" },
                                        { title: "double_click(ele_or_loc=None)", description: "双击", type: "method", code: "Actions(page).double_click()" },
                                        { title: "hold(ele_or_loc=None)", description: "按住鼠标", type: "method", code: "Actions(page).hold()" },
                                        { title: "release()", description: "释放鼠标", type: "method", code: "Actions(page).release()" }
                                    ]
                                },
                                {
                                    title: "键盘操作",
                                    type: "group",
                                    children: [
                                        { title: "key_down(key)", description: "按下按键", type: "method", code: "Actions(page).key_down('ctrl')" },
                                        { title: "key_up(key)", description: "释放按键", type: "method", code: "Actions(page).key_up('ctrl')" },
                                        { title: "type(text, interval=0.05)", description: "输入文本", type: "method", code: "Actions(page).type('Hello')" },
                                        { title: "input(text)", description: "快速输入", type: "method", code: "Actions(page).input('text')" }
                                    ]
                                },
                                {
                                    title: "滚动操作",
                                    type: "group",
                                    children: [
                                        { title: "scroll(delta_x=0, delta_y=0, count=3)", description: "滚动", type: "method", code: "Actions(page).scroll(0, -3)" },
                                        { title: "wait(second)", description: "等待", type: "method", code: "Actions(page).wait(1)" }
                                    ]
                                }
                            ]
                        },
                        {
                            title: "Keys类",
                            description: "按键常量定义",
                            type: "class",
                            children: [
                                {
                                    title: "修饰键",
                                    type: "group",
                                    children: [
                                        { title: "CTRL", description: "Ctrl键", type: "property", code: "Keys.CTRL" },
                                        { title: "ALT", description: "Alt键", type: "property", code: "Keys.ALT" },
                                        { title: "SHIFT", description: "Shift键", type: "property", code: "Keys.SHIFT" },
                                        { title: "META", description: "Meta键（Win键）", type: "property", code: "Keys.META" }
                                    ]
                                },
                                {
                                    title: "功能键",
                                    type: "group",
                                    children: [
                                        { title: "ENTER", description: "回车键", type: "property", code: "Keys.ENTER" },
                                        { title: "TAB", description: "Tab键", type: "property", code: "Keys.TAB" },
                                        { title: "ESC", description: "Esc键", type: "property", code: "Keys.ESC" },
                                        { title: "SPACE", description: "空格键", type: "property", code: "Keys.SPACE" },
                                        { title: "BACKSPACE", description: "退格键", type: "property", code: "Keys.BACKSPACE" },
                                        { title: "DELETE", description: "删除键", type: "property", code: "Keys.DELETE" }
                                    ]
                                },
                                {
                                    title: "方向键",
                                    type: "group",
                                    children: [
                                        { title: "UP", description: "上箭头", type: "property", code: "Keys.UP" },
                                        { title: "DOWN", description: "下箭头", type: "property", code: "Keys.DOWN" },
                                        { title: "LEFT", description: "左箭头", type: "property", code: "Keys.LEFT" },
                                        { title: "RIGHT", description: "右箭头", type: "property", code: "Keys.RIGHT" }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    title: "错误处理",
                    description: "异常类型和错误处理机制",
                    type: "category",
                    children: [
                        {
                            title: "异常类型",
                            description: "DrissionPage定义的异常类",
                            type: "module",
                            children: [
                                {
                                    title: "元素异常",
                                    type: "group",
                                    children: [
                                        { title: "ElementNotFoundError", description: "元素未找到异常", type: "class", code: "try:\n    element = page.ele('#not-exist')\nexcept ElementNotFoundError:\n    print('元素不存在')" },
                                        { title: "ElementLostError", description: "元素丢失异常", type: "class", code: "try:\n    element.click()\nexcept ElementLostError:\n    print('元素已失效')" }
                                    ]
                                },
                                {
                                    title: "页面异常",
                                    type: "group",
                                    children: [
                                        { title: "ContextLostError", description: "页面上下文丢失", type: "class", code: "try:\n    page.get(url)\nexcept ContextLostError:\n    print('页面上下文丢失')" },
                                        { title: "PageDisconnectedError", description: "页面连接断开", type: "class", code: "try:\n    page.title\nexcept PageDisconnectedError:\n    print('页面连接断开')" }
                                    ]
                                },
                                {
                                    title: "网络异常",
                                    type: "group",
                                    children: [
                                        { title: "TimeoutError", description: "超时异常", type: "class", code: "try:\n    page.wait.ele_displayed('#slow', timeout=5)\nexcept TimeoutError:\n    print('等待超时')" },
                                        { title: "ConnectionError", description: "连接异常", type: "class", code: "try:\n    page.get('http://invalid-url')\nexcept ConnectionError:\n    print('连接失败')" }
                                    ]
                                }
                            ]
                        },
                        {
                            title: "错误处理策略",
                            description: "处理错误的不同策略",
                            type: "module",
                            children: [
                                {
                                    title: "全局设置",
                                    type: "group",
                                    children: [
                                        { title: "Settings.raise_when_ele_not_found", description: "元素未找到时是否抛异常", type: "property", code: "Settings.raise_when_ele_not_found = False" },
                                        { title: "Settings.raise_when_click_failed", description: "点击失败时是否抛异常", type: "property", code: "Settings.raise_when_click_failed = True" }
                                    ]
                                },
                                {
                                    title: "重试机制",
                                    type: "group",
                                    children: [
                                        { title: "自动重试", description: "内置重试机制", type: "feature", code: "page.get(url, retry=3, interval=1)" },
                                        { title: "手动重试", description: "手动实现重试", type: "feature", code: "for i in range(3):\n    try:\n        page.get(url)\n        break\n    except:\n        time.sleep(1)" }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    title: "定位器语法",
                    description: "元素定位的各种语法规则",
                    type: "category",
                    children: [
                        {
                            title: "CSS选择器",
                            description: "标准CSS选择器语法",
                            type: "module",
                            children: [
                                {
                                    title: "基础选择器",
                                    type: "group",
                                    children: [
                                        { title: "#id", description: "ID选择器", type: "syntax", code: "page.ele('#myid')" },
                                        { title: ".class", description: "类选择器", type: "syntax", code: "page.ele('.my-class')" },
                                        { title: "tag", description: "标签选择器", type: "syntax", code: "page.ele('div')" },
                                        { title: "[attr]", description: "属性选择器", type: "syntax", code: "page.ele('[data-id]')" },
                                        { title: "[attr=value]", description: "属性值选择器", type: "syntax", code: "page.ele('[type=\"text\"]')" }
                                    ]
                                },
                                {
                                    title: "组合选择器",
                                    type: "group",
                                    children: [
                                        { title: "parent child", description: "后代选择器", type: "syntax", code: "page.ele('div span')" },
                                        { title: "parent > child", description: "子元素选择器", type: "syntax", code: "page.ele('ul > li')" },
                                        { title: "prev + next", description: "相邻兄弟选择器", type: "syntax", code: "page.ele('h1 + p')" },
                                        { title: "prev ~ siblings", description: "通用兄弟选择器", type: "syntax", code: "page.ele('h1 ~ p')" }
                                    ]
                                }
                            ]
                        },
                        {
                            title: "XPath表达式",
                            description: "XPath定位语法",
                            type: "module",
                            children: [
                                {
                                    title: "基础XPath",
                                    type: "group",
                                    children: [
                                        { title: "//tag", description: "查找所有标签", type: "syntax", code: "page.ele('//div')" },
                                        { title: "//tag[@attr='value']", description: "属性匹配", type: "syntax", code: "page.ele('//input[@type=\"text\"]')" },
                                        { title: "//tag[text()='text']", description: "文本匹配", type: "syntax", code: "page.ele('//button[text()=\"提交\"]')" },
                                        { title: "//tag[contains(@attr, 'value')]", description: "属性包含", type: "syntax", code: "page.ele('//div[contains(@class, \"item\")]')" }
                                    ]
                                }
                            ]
                        },
                        {
                            title: "DrissionPage语法",
                            description: "DrissionPage特有的定位语法",
                            type: "module",
                            children: [
                                {
                                    title: "标签语法",
                                    type: "group",
                                    children: [
                                        { title: "tag:tagname", description: "标签名定位", type: "syntax", code: "page.ele('tag:div')" },
                                        { title: "text:content", description: "文本内容定位", type: "syntax", code: "page.ele('text:点击这里')" },
                                        { title: "text*:partial", description: "部分文本匹配", type: "syntax", code: "page.ele('text*:点击')" }
                                    ]
                                },
                                {
                                    title: "属性语法",
                                    type: "group",
                                    children: [
                                        { title: "@attr=value", description: "属性等于", type: "syntax", code: "page.ele('@id=myid')" },
                                        { title: "@attr*=value", description: "属性包含", type: "syntax", code: "page.ele('@class*=btn')" },
                                        { title: "@attr^=value", description: "属性开头", type: "syntax", code: "page.ele('@href^=https')" },
                                        { title: "@attr$=value", description: "属性结尾", type: "syntax", code: "page.ele('@src$=.jpg')" }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        };

        // 渲染思维导图
        function renderMindmap(data, container, level = 0) {
            const nodeDiv = document.createElement('div');
            nodeDiv.className = `node level-${level}`;

            const contentDiv = document.createElement('div');
            contentDiv.className = 'node-content';

            if (data.children && data.children.length > 0) {
                const toggle = document.createElement('div');
                toggle.className = 'toggle collapsed';
                toggle.onclick = () => toggleNode(toggle);
                contentDiv.appendChild(toggle);
            }

            const titleSpan = document.createElement('span');
            titleSpan.className = 'node-title';
            titleSpan.textContent = data.title;
            contentDiv.appendChild(titleSpan);

            if (data.description) {
                const descSpan = document.createElement('span');
                descSpan.className = 'node-description';
                descSpan.textContent = data.description;
                contentDiv.appendChild(descSpan);
            }

            if (data.type) {
                const typeSpan = document.createElement('span');
                typeSpan.className = `api-tag ${data.type}`;
                typeSpan.textContent = data.type;
                contentDiv.appendChild(typeSpan);
            }

            nodeDiv.appendChild(contentDiv);

            if (data.code) {
                const codeDiv = document.createElement('div');
                codeDiv.className = 'code-example';
                codeDiv.innerHTML = `<pre><code>${data.code}</code></pre>`;
                nodeDiv.appendChild(codeDiv);

                contentDiv.onclick = () => {
                    codeDiv.classList.toggle('show');
                };
            }

            if (data.children && data.children.length > 0) {
                const childrenDiv = document.createElement('div');
                childrenDiv.className = 'children';

                data.children.forEach(child => {
                    renderMindmap(child, childrenDiv, level + 1);
                });

                nodeDiv.appendChild(childrenDiv);
            }

            container.appendChild(nodeDiv);
        }

        function toggleNode(toggle) {
            const isCollapsed = toggle.classList.contains('collapsed');
            const children = toggle.closest('.node').querySelector('.children');

            if (isCollapsed) {
                toggle.classList.remove('collapsed');
                toggle.classList.add('expanded');
                if (children) children.classList.add('expanded');
            } else {
                toggle.classList.add('collapsed');
                toggle.classList.remove('expanded');
                if (children) children.classList.remove('expanded');
            }
        }

        function expandAll() {
            document.querySelectorAll('.toggle').forEach(toggle => {
                toggle.classList.remove('collapsed');
                toggle.classList.add('expanded');
            });
            document.querySelectorAll('.children').forEach(children => {
                children.classList.add('expanded');
            });
        }

        function collapseAll() {
            document.querySelectorAll('.toggle').forEach(toggle => {
                toggle.classList.add('collapsed');
                toggle.classList.remove('expanded');
            });
            document.querySelectorAll('.children').forEach(children => {
                children.classList.remove('expanded');
            });
        }

        function showCodeExamples() {
            document.querySelectorAll('.code-example').forEach(code => {
                code.classList.toggle('show');
            });
        }

        function searchAPI() {
            const query = document.getElementById('searchInput').value.toLowerCase();
            const nodes = document.querySelectorAll('.node-content');

            nodes.forEach(node => {
                const text = node.textContent.toLowerCase();
                const nodeElement = node.closest('.node');

                if (text.includes(query)) {
                    nodeElement.style.display = 'block';
                    node.classList.add('highlight');
                } else {
                    if (query) {
                        nodeElement.style.display = 'none';
                    } else {
                        nodeElement.style.display = 'block';
                    }
                    node.classList.remove('highlight');
                }
            });
        }

        // 视图切换功能
        let currentView = 'list';
        let visualMindmapInitialized = false;

        function switchView(view) {
            currentView = view;

            // 更新按钮状态
            document.getElementById('listViewBtn').classList.toggle('active', view === 'list');
            document.getElementById('graphViewBtn').classList.toggle('active', view === 'graph');

            // 切换视图
            document.getElementById('mindmap').style.display = view === 'list' ? 'block' : 'none';
            document.getElementById('visualMindmap').classList.toggle('active', view === 'graph');

            // 初始化可视化思维导图
            if (view === 'graph' && !visualMindmapInitialized) {
                initVisualMindmap();
                visualMindmapInitialized = true;
            }
        }

        // 可视化思维导图变量
        let svg, g, tree, root, zoom;
        const width = 1200;
        const height = 800;

        // 节点颜色映射
        const nodeColors = {
            'root': '#e74c3c',
            'category': '#3498db',
            'module': '#2ecc71',
            'class': '#f39c12',
            'group': '#9b59b6',
            'method': '#9b59b6',
            'property': '#9b59b6',
            'syntax': '#34495e',
            'feature': '#16a085'
        };

        function initVisualMindmap() {
            const container = d3.select('#visualMindmap');

            // 创建SVG
            svg = container.append('svg')
                .attr('width', '100%')
                .attr('height', '100%')
                .attr('viewBox', `0 0 ${width} ${height}`);

            // 添加缩放功能
            zoom = d3.zoom()
                .scaleExtent([0.1, 3])
                .on('zoom', (event) => {
                    g.attr('transform', event.transform);
                });

            svg.call(zoom);

            // 创建主容器组
            g = svg.append('g');

            // 创建树布局
            tree = d3.tree().size([height - 100, width - 200]);

            // 处理数据
            root = d3.hierarchy(mindmapData);
            root.x0 = height / 2;
            root.y0 = 0;

            // 初始化时收起所有子节点
            root.children.forEach(collapse);

            update(root);

            // 居中显示
            const bounds = g.node().getBBox();
            const fullWidth = width;
            const fullHeight = height;
            const widthRatio = fullWidth / bounds.width;
            const heightRatio = fullHeight / bounds.height;
            const scale = Math.min(widthRatio, heightRatio) * 0.8;
            const translate = [
                fullWidth / 2 - scale * (bounds.x + bounds.width / 2),
                fullHeight / 2 - scale * (bounds.y + bounds.height / 2)
            ];

            svg.call(zoom.transform, d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale));
        }

        function collapse(d) {
            if (d.children) {
                d._children = d.children;
                d._children.forEach(collapse);
                d.children = null;
            }
        }

        function update(source) {
            // 计算新的树布局
            const treeData = tree(root);
            const nodes = treeData.descendants();
            const links = treeData.descendants().slice(1);

            // 标准化固定深度
            nodes.forEach(d => { d.y = d.depth * 180; });

            // 更新节点
            const node = g.selectAll('g.node')
                .data(nodes, d => d.id || (d.id = ++i));

            // 进入新节点
            const nodeEnter = node.enter().append('g')
                .attr('class', 'node')
                .attr('transform', d => `translate(${source.y0},${source.x0})`)
                .on('click', click);

            // 添加文本（先添加以便计算尺寸）
            nodeEnter.append('text')
                .attr('class', 'node-text')
                .attr('dy', '0.35em')
                .attr('text-anchor', 'middle')
                .text(d => d.data.title)
                .style('fill-opacity', 1e-6);

            // 添加矩形（基于文本尺寸）
            nodeEnter.insert('rect', 'text')
                .attr('class', 'node-rect')
                .attr('width', 1e-6)
                .attr('height', 1e-6)
                .attr('x', 0)
                .attr('y', 0)
                .style('fill', d => nodeColors[d.data.type] || '#95a5a6')
                .style('stroke', d => nodeColors[d.data.type] || '#95a5a6');

            // 更新现有节点
            const nodeUpdate = nodeEnter.merge(node);

            nodeUpdate.transition()
                .duration(750)
                .attr('transform', d => `translate(${d.y},${d.x})`);

            // 计算文本尺寸并更新矩形
            nodeUpdate.each(function(d) {
                const textElement = d3.select(this).select('text');
                const rectElement = d3.select(this).select('rect');

                // 获取文本边界框
                const bbox = textElement.node().getBBox();
                const padding = 12;
                const minWidth = 60;
                const minHeight = 24;

                const rectWidth = Math.max(bbox.width + padding * 2, minWidth);
                const rectHeight = Math.max(bbox.height + padding, minHeight);

                rectElement
                    .attr('width', rectWidth)
                    .attr('height', rectHeight)
                    .attr('x', -rectWidth / 2)
                    .attr('y', -rectHeight / 2)
                    .style('fill', nodeColors[d.data.type] || '#95a5a6')
                    .attr('cursor', 'pointer');

                // 更新文本颜色
                textElement
                    .style('fill-opacity', 1)
                    .style('fill', '#fff')
                    .attr('text-anchor', 'middle');
            });

            // 移除退出的节点
            const nodeExit = node.exit().transition()
                .duration(750)
                .attr('transform', d => `translate(${source.y},${source.x})`)
                .remove();

            nodeExit.select('rect')
                .attr('width', 1e-6)
                .attr('height', 1e-6);

            nodeExit.select('text')
                .style('fill-opacity', 1e-6);

            // 更新链接
            const link = g.selectAll('path.link')
                .data(links, d => d.id);

            const linkEnter = link.enter().insert('path', 'g')
                .attr('class', 'link')
                .attr('d', d => {
                    const o = {x: source.x0, y: source.y0};
                    return diagonal(o, o);
                });

            const linkUpdate = linkEnter.merge(link);

            linkUpdate.transition()
                .duration(750)
                .attr('d', d => diagonal(d, d.parent));

            const linkExit = link.exit().transition()
                .duration(750)
                .attr('d', d => {
                    const o = {x: source.x, y: source.y};
                    return diagonal(o, o);
                })
                .remove();

            // 存储旧位置用于过渡
            nodes.forEach(d => {
                d.x0 = d.x;
                d.y0 = d.y;
            });
        }

        // 创建对角线路径
        function diagonal(s, d) {
            return `M ${s.y} ${s.x}
                    C ${(s.y + d.y) / 2} ${s.x},
                      ${(s.y + d.y) / 2} ${d.x},
                      ${d.y} ${d.x}`;
        }

        // 节点点击事件
        function click(event, d) {
            if (d.children) {
                d._children = d.children;
                d.children = null;
            } else {
                d.children = d._children;
                d._children = null;
            }
            update(d);

            // 显示节点信息
            showNodeInfo(d.data);
        }

        let i = 0; // 用于生成唯一ID

        // 显示节点信息
        function showNodeInfo(nodeData) {
            const panel = document.getElementById('infoPanel');
            const title = document.getElementById('infoTitle');
            const description = document.getElementById('infoDescription');
            const code = document.getElementById('infoCode');

            title.textContent = nodeData.title;
            description.textContent = nodeData.description || '暂无描述';

            if (nodeData.code) {
                code.style.display = 'block';
                code.innerHTML = `<pre><code>${nodeData.code}</code></pre>`;
            } else {
                code.style.display = 'none';
            }

            panel.classList.add('show');
        }

        // 关闭信息面板
        function closeInfoPanel() {
            document.getElementById('infoPanel').classList.remove('show');
        }

        // 重置缩放
        function resetZoom() {
            if (svg && zoom) {
                svg.transition().duration(750).call(
                    zoom.transform,
                    d3.zoomIdentity
                );
            }
        }

        // 适应屏幕
        function fitToScreen() {
            if (g && svg && zoom) {
                const bounds = g.node().getBBox();
                const fullWidth = width;
                const fullHeight = height;
                const widthRatio = fullWidth / bounds.width;
                const heightRatio = fullHeight / bounds.height;
                const scale = Math.min(widthRatio, heightRatio) * 0.8;
                const translate = [
                    fullWidth / 2 - scale * (bounds.x + bounds.width / 2),
                    fullHeight / 2 - scale * (bounds.y + bounds.height / 2)
                ];

                svg.transition().duration(750).call(
                    zoom.transform,
                    d3.zoomIdentity.translate(translate[0], translate[1]).scale(scale)
                );
            }
        }

        // 展开所有节点
        function expandAllNodes() {
            if (root) {
                function expand(d) {
                    if (d._children) {
                        d.children = d._children;
                        d._children = null;
                    }
                    if (d.children) {
                        d.children.forEach(expand);
                    }
                }
                expand(root);
                update(root);
            }
        }

        // 收起所有节点
        function collapseAllNodes() {
            if (root) {
                function collapse(d) {
                    if (d.children) {
                        d._children = d.children;
                        d.children = null;
                        d._children.forEach(collapse);
                    }
                }
                root.children.forEach(collapse);
                update(root);
            }
        }

        // 搜索功能增强
        function searchInGraph(query) {
            if (!root || currentView !== 'graph') return;

            const nodes = g.selectAll('g.node');

            if (!query) {
                // 清除高亮
                nodes.select('rect').style('stroke-width', 2);
                nodes.select('text').style('font-weight', 'normal');
                return;
            }

            nodes.each(function(d) {
                const node = d3.select(this);
                const rect = node.select('rect');
                const text = node.select('text');

                if (d.data.title.toLowerCase().includes(query.toLowerCase()) ||
                    (d.data.description && d.data.description.toLowerCase().includes(query.toLowerCase()))) {
                    // 高亮匹配的节点
                    rect.style('stroke-width', 4).style('stroke', '#e74c3c');
                    text.style('font-weight', 'bold');

                    // 展开到该节点的路径
                    let current = d.parent;
                    while (current) {
                        if (current._children) {
                            current.children = current._children;
                            current._children = null;
                        }
                        current = current.parent;
                    }
                } else {
                    // 恢复默认样式
                    rect.style('stroke-width', 2).style('stroke', nodeColors[d.data.type] || '#95a5a6');
                    text.style('font-weight', 'normal');
                }
            });

            if (query) {
                update(root);
            }
        }

        // 修改原有搜索函数以支持图形视图
        function searchAPI() {
            const query = document.getElementById('searchInput').value.toLowerCase();

            if (currentView === 'list') {
                // 原有的列表搜索逻辑
                const nodes = document.querySelectorAll('.node-content');
                nodes.forEach(node => {
                    const text = node.textContent.toLowerCase();
                    const nodeElement = node.closest('.node');

                    if (text.includes(query)) {
                        nodeElement.style.display = 'block';
                        node.classList.add('highlight');
                    } else {
                        if (query) {
                            nodeElement.style.display = 'none';
                        } else {
                            nodeElement.style.display = 'block';
                        }
                        node.classList.remove('highlight');
                    }
                });
            } else {
                // 图形视图搜索
                searchInGraph(query);
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            const mindmapContainer = document.getElementById('mindmap');
            renderMindmap(mindmapData, mindmapContainer);

            document.getElementById('searchInput').addEventListener('input', searchAPI);
        });
    </script>
</body>
</html>
