<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DrissionPage 库详解</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section h2 {
            color: #4a5568;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
            font-size: 1.8em;
        }
        
        .section h3 {
            color: #2d3748;
            margin: 20px 0 10px 0;
            font-size: 1.3em;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .feature-card h4 {
            color: #667eea;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            position: relative;
        }
        
        .code-block::before {
            content: 'Python';
            position: absolute;
            top: 5px;
            right: 10px;
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.8em;
        }
        
        .architecture-diagram {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        
        .mode-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .mode-card {
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 20px;
            background: #f8f9ff;
        }
        
        .mode-card h4 {
            color: #667eea;
            margin-bottom: 15px;
            text-align: center;
            font-size: 1.3em;
        }
        
        .pros-cons {
            margin-top: 15px;
        }
        
        .pros, .cons {
            margin: 10px 0;
        }
        
        .pros h5 {
            color: #38a169;
            margin-bottom: 5px;
        }
        
        .cons h5 {
            color: #e53e3e;
            margin-bottom: 5px;
        }
        
        .pros ul, .cons ul {
            margin-left: 20px;
        }
        
        .workflow-steps {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .step {
            background: #667eea;
            color: white;
            padding: 15px;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 10px;
        }
        
        .arrow {
            font-size: 1.5em;
            color: #667eea;
        }
        
        @media (max-width: 768px) {
            .mode-comparison {
                grid-template-columns: 1fr;
            }
            
            .workflow-steps {
                flex-direction: column;
            }
            
            .arrow {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 DrissionPage</h1>
            <p>强大的Python Web自动化库 - 浏览器控制与HTTP请求的完美结合</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📖 什么是 DrissionPage？</h2>
                <div class="highlight-box">
                    <p><strong>DrissionPage</strong> 是一个功能强大的Python Web自动化库，它将浏览器控制能力与HTTP请求处理相结合，为Web自动化任务提供统一的接口。</p>
                </div>
                
                <h3>🎯 核心特性</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🔄 多种操作模式</h4>
                        <p>支持浏览器模式、会话模式和混合模式，可根据需求灵活选择</p>
                    </div>
                    <div class="feature-card">
                        <h4>⚡ 高性能</h4>
                        <p>基于Chrome DevTools Protocol，比传统WebDriver更快更稳定</p>
                    </div>
                    <div class="feature-card">
                        <h4>🎨 简化语法</h4>
                        <p>提供简洁的元素定位语法，减少代码复杂度</p>
                    </div>
                    <div class="feature-card">
                        <h4>🛡️ 内置等待机制</h4>
                        <p>自动处理页面加载和元素等待，提高脚本稳定性</p>
                    </div>
                    <div class="feature-card">
                        <h4>🌐 跨框架支持</h4>
                        <p>支持iframe、Shadow DOM等复杂页面结构</p>
                    </div>
                    <div class="feature-card">
                        <h4>📱 多标签页操作</h4>
                        <p>无需切换上下文即可操作多个标签页</p>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🏗️ 系统架构</h2>
                <div class="architecture-diagram">
                    <h3>DrissionPage 架构图</h3>
                    <p style="margin: 20px 0;">
                        <strong>用户代码</strong> ↔ <strong>DrissionPage API</strong> ↔ <strong>浏览器/HTTP</strong>
                    </p>
                    <div style="display: flex; justify-content: space-around; margin-top: 20px;">
                        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px;">
                            <strong>ChromiumPage</strong><br>
                            浏览器控制
                        </div>
                        <div style="background: #f3e5f5; padding: 15px; border-radius: 8px;">
                            <strong>SessionPage</strong><br>
                            HTTP请求
                        </div>
                        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px;">
                            <strong>WebPage</strong><br>
                            混合模式
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🔧 安装与配置</h2>
                <h3>安装</h3>
                <div class="code-block">
pip install DrissionPage
                </div>
                
                <h3>基本配置</h3>
                <div class="code-block">
from DrissionPage import ChromiumPage, SessionPage, WebPage
from DrissionPage import ChromiumOptions, SessionOptions

# 配置浏览器选项
options = ChromiumOptions()
options.set_browser_path('/path/to/chrome')  # 设置浏览器路径
options.set_user_data_path('/path/to/user_data')  # 设置用户数据目录
options.headless()  # 无头模式

# 创建页面对象
page = ChromiumPage(options)
                </div>
            </div>

            <div class="section">
                <h2>🎭 操作模式对比</h2>
                <div class="mode-comparison">
                    <div class="mode-card">
                        <h4>🌐 浏览器模式 (ChromiumPage)</h4>
                        <p>通过Chrome DevTools Protocol控制真实浏览器实例</p>
                        <div class="pros-cons">
                            <div class="pros">
                                <h5>✅ 优势</h5>
                                <ul>
                                    <li>完整的JavaScript支持</li>
                                    <li>真实用户交互模拟</li>
                                    <li>处理复杂页面结构</li>
                                    <li>支持文件上传下载</li>
                                </ul>
                            </div>
                            <div class="cons">
                                <h5>❌ 劣势</h5>
                                <ul>
                                    <li>资源消耗较大</li>
                                    <li>速度相对较慢</li>
                                    <li>需要浏览器环境</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="mode-card">
                        <h4>⚡ 会话模式 (SessionPage)</h4>
                        <p>基于HTTP请求的轻量级自动化方案</p>
                        <div class="pros-cons">
                            <div class="pros">
                                <h5>✅ 优势</h5>
                                <ul>
                                    <li>速度快，资源消耗少</li>
                                    <li>适合大规模数据抓取</li>
                                    <li>无需浏览器环境</li>
                                    <li>易于部署</li>
                                </ul>
                            </div>
                            <div class="cons">
                                <h5>❌ 劣势</h5>
                                <ul>
                                    <li>不支持JavaScript</li>
                                    <li>无法处理动态内容</li>
                                    <li>有限的交互能力</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>💻 代码示例</h2>

                <h3>🌐 浏览器模式示例</h3>
                <div class="code-block">
from DrissionPage import ChromiumPage

# 创建页面对象
page = ChromiumPage()

# 访问网页
page.get('https://www.example.com')

# 查找元素并交互
search_box = page.ele('#search-input')
search_box.input('DrissionPage')

# 点击按钮
search_btn = page.ele('tag:button@text()=搜索')
search_btn.click()

# 等待结果加载
page.wait.load_start()

# 获取结果
results = page.eles('.result-item')
for result in results:
    title = result.ele('.title').text
    link = result.ele('tag:a').attr('href')
    print(f'{title}: {link}')
                </div>

                <h3>⚡ 会话模式示例</h3>
                <div class="code-block">
from DrissionPage import SessionPage

# 创建会话页面
page = SessionPage()

# 设置请求头
page.set.headers({'User-Agent': 'Custom Agent'})

# 发送GET请求
page.get('https://api.example.com/data')

# 解析JSON响应
data = page.json
print(data)

# 发送POST请求
response = page.post('https://api.example.com/submit',
                    json={'key': 'value'})

# 处理HTML内容
page.get('https://www.example.com')
titles = page.eles('tag:h2')
for title in titles:
    print(title.text)
                </div>

                <h3>🔄 混合模式示例</h3>
                <div class="code-block">
from DrissionPage import WebPage

# 创建混合页面对象
page = WebPage()

# 使用浏览器模式登录
page.get('https://example.com/login')
page.ele('#username').input('user123')
page.ele('#password').input('password123')
page.ele('#login-btn').click()

# 等待登录完成
page.wait.url_change()

# 切换到会话模式进行数据抓取
page.change_mode('s')  # 切换到session模式

# 快速抓取数据
page.get('https://example.com/api/data')
data = page.json

# 切换回浏览器模式
page.change_mode('d')  # 切换到driver模式
                </div>
            </div>

            <div class="section">
                <h2>🔍 元素定位</h2>
                <h3>DrissionPage 简化语法</h3>
                <div class="code-block">
# 传统XPath/CSS选择器
element1 = page.ele('xpath://div[@id="content"]')
element2 = page.ele('css:#content')

# DrissionPage简化语法
element3 = page.ele('#content')  # ID选择器
element4 = page.ele('.class-name')  # 类选择器
element5 = page.ele('tag:div')  # 标签选择器
element6 = page.ele('text:点击这里')  # 文本内容
element7 = page.ele('tag:input@type=text')  # 属性匹配
element8 = page.ele('tag:a@href*=github')  # 属性包含

# 复合条件
element9 = page.ele('tag:div@class=container@text():包含文本')
                </div>

                <h3>元素集合操作</h3>
                <div class="code-block">
# 获取多个元素
links = page.eles('tag:a')

# 过滤元素
visible_links = links.filter.displayed()
external_links = links.filter.attr('href*=http')

# 批量获取属性
urls = links.get.attrs('href')
texts = links.get.texts()

# 链式过滤
important_links = (page.eles('tag:a')
                  .filter.displayed()
                  .filter.attr('class*=important')
                  .filter.text('下载'))
                </div>
            </div>

            <div class="section">
                <h2>⏱️ 等待与同步</h2>
                <div class="code-block">
# 等待元素出现
page.wait.ele_displayed('#dynamic-content')

# 等待元素消失
page.wait.ele_hidden('.loading-spinner')

# 等待页面加载完成
page.wait.load_start()
page.wait.doc_loaded()

# 等待URL变化
page.wait.url_change()

# 自定义等待条件
page.wait(lambda: page.ele('#result').text != '', timeout=10)

# 元素级等待
element = page.ele('#button')
element.wait.clickable()  # 等待可点击
element.wait.enabled()    # 等待启用
                </div>
            </div>

            <div class="section">
                <h2>🌟 高级功能</h2>

                <h3>📡 网络监控</h3>
                <div class="code-block">
# 监听网络请求
page.listen.set_targets('api/data')
page.listen.start()

# 执行操作
page.ele('#load-data').click()

# 获取网络响应
packet = page.listen.wait()
response_data = packet.response.body
print(response_data)
                </div>

                <h3>📁 文件下载</h3>
                <div class="code-block">
# 设置下载路径
page.set.download_path('./downloads')

# 监听下载
page.listen.download()

# 触发下载
page.ele('#download-btn').click()

# 等待下载完成
download_info = page.listen.wait_download()
print(f'下载完成: {download_info.path}')
                </div>

                <h3>🎯 JavaScript执行</h3>
                <div class="code-block">
# 执行JavaScript
result = page.run_js('return document.title')
print(result)

# 在元素上执行JavaScript
element = page.ele('#my-element')
element.run_js('this.style.backgroundColor = "red"')

# 异步JavaScript
page.run_js_loaded('console.log("页面加载完成")')
                </div>
            </div>

            <div class="section">
                <h2>🚀 最佳实践</h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🎯 选择合适的模式</h4>
                        <p>静态内容用SessionPage，动态内容用ChromiumPage，复杂场景用WebPage混合模式</p>
                    </div>
                    <div class="feature-card">
                        <h4>⏱️ 合理使用等待</h4>
                        <p>避免固定时间等待，使用条件等待提高脚本稳定性和效率</p>
                    </div>
                    <div class="feature-card">
                        <h4>🔍 精确元素定位</h4>
                        <p>使用最具体的定位器，避免过于宽泛的选择器</p>
                    </div>
                    <div class="feature-card">
                        <h4>🛡️ 异常处理</h4>
                        <p>合理处理网络异常、元素不存在等常见错误情况</p>
                    </div>
                    <div class="feature-card">
                        <h4>📊 性能优化</h4>
                        <p>批量操作使用元素集合，大量数据抓取优先使用SessionPage</p>
                    </div>
                    <div class="feature-card">
                        <h4>🔧 配置管理</h4>
                        <p>使用配置文件管理不同环境的设置，提高代码可维护性</p>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>📚 总结</h2>
                <div class="highlight-box">
                    <h3>🎉 为什么选择 DrissionPage？</h3>
                    <ul style="margin-top: 15px; line-height: 1.8;">
                        <li><strong>🔄 灵活性</strong>：三种操作模式满足不同场景需求</li>
                        <li><strong>⚡ 高性能</strong>：基于CDP协议，比WebDriver更快更稳定</li>
                        <li><strong>🎨 易用性</strong>：简化的API设计，降低学习成本</li>
                        <li><strong>🛡️ 稳定性</strong>：内置等待机制和错误处理</li>
                        <li><strong>🌐 兼容性</strong>：支持各种复杂页面结构</li>
                        <li><strong>📈 可扩展</strong>：丰富的高级功能支持复杂自动化需求</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 30px; color: #666;">
                    <p>🔗 <strong>官方文档</strong>：<a href="https://github.com/g1879/DrissionPage" style="color: #667eea;">https://github.com/g1879/DrissionPage</a></p>
                    <p style="margin-top: 10px;">📝 本文档基于最新版本DrissionPage编写</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
