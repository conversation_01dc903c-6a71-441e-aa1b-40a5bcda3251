# 📚 Python库/框架学习思维导图需求文档

## 🎯 核心需求

为指定的库/框架创建**详细的HTML交互式思维导图**，用于系统性学习和API参考。

### 目标库/框架
- **库名称**: `{LIBRARY_NAME}`
- **版本要求**: 最新稳定版本

---

## 🛠️ 工具使用要求

### 必须使用的工具组合
1. **deepwiki工具**
   - 获取`{LIBRARY_NAME}`的完整文档结构
   - 提取概念说明和架构信息
   - 了解库的设计理念和核心特性

2. **context7工具**
   - 获取`{LIBRARY_NAME}`的最新代码示例
   - 提取实际API使用方法
   - 收集真实的编程模式和最佳实践

3. **sequentialthinking工具**
   - 分析和整理收集到的信息
   - 构建逻辑清晰的思维导图结构
   - 确保内容的完整性和准确性

### 工具使用流程
```
deepwiki → context7 → sequentialthinking → HTML生成 → 浏览器验证
```

---

## 📋 输出格式要求

### 文件格式
- **格式**: HTML文件
- **文件名**: `{library_name}_complete_mindmap.html`
- **位置**: `docs/` 目录下

### 交互式功能
- ✅ **搜索功能**: 支持API方法、类名、功能关键词搜索
- ✅ **折叠/展开**: 支持节点的展开全部、收起全部操作
- ✅ **代码示例**: 点击节点显示具体使用代码
- ✅ **分层着色**: 不同层级使用不同颜色区分
- ✅ **响应式设计**: 适配桌面和移动设备

### 视觉设计
- **主题色彩**: 渐变背景，现代化UI设计
- **层级区分**: 6个层级，每层不同颜色
- **标签系统**: method、property、class、event等API类型标签
- **统计信息**: 显示模块数量、API数量、代码示例数量

---

## 📖 内容结构要求

### 思维导图层级结构
```
{LIBRARY_NAME} (根节点)
├── 核心架构
│   ├── 基础组件
│   ├── 核心类
│   └── 配置系统
├── 主要功能模块
│   ├── 功能分类1
│   ├── 功能分类2
│   └── 功能分类3
├── API参考
│   ├── 类方法
│   ├── 实例方法
│   ├── 属性
│   └── 事件
├── 高级特性
│   ├── 扩展功能
│   ├── 插件系统
│   └── 自定义配置
├── 工具和辅助
│   ├── 辅助类
│   ├── 工具函数
│   └── 常量定义
├── 错误处理
│   ├── 异常类型
│   ├── 错误处理策略
│   └── 调试方法
└── 最佳实践
    ├── 使用模式
    ├── 性能优化
    └── 常见问题
```

### 节点内容要求
每个节点必须包含：
- **标题**: 清晰的API名称或功能名称
- **描述**: 简洁的功能说明
- **类型标签**: method/property/class/event等
- **代码示例**: 实际可运行的代码片段
- **参数说明**: 方法参数和返回值（如适用）

---

## 🔍 详细程度要求

### 完整性标准
- ✅ **API覆盖率**: 覆盖所有主要的类、方法、属性
- ✅ **代码示例**: 每个重要API都要有使用示例
- ✅ **功能分类**: 按功能逻辑分组，不遗漏重要特性
- ✅ **层次清晰**: 从概念到具体实现的完整路径

### 质量标准
- **准确性**: 基于最新文档和代码示例
- **实用性**: 代码示例可直接使用
- **可读性**: 描述简洁明了，易于理解
- **完整性**: 不遗漏重要的API和功能

---

## 📊 统计信息要求

在思维导图头部显示：
- 📚 核心模块数量
- 🔧 API方法总数
- 💡 代码示例数量
- 🎯 功能覆盖范围

---

## 🚀 工作流程模板

### 第一步：信息收集
```bash
# 使用deepwiki获取文档结构
read_wiki_structure_deepwiki({LIBRARY_REPO})
read_wiki_contents_deepwiki({LIBRARY_REPO})

# 使用context7获取代码示例
resolve-library-id_Context_7({LIBRARY_NAME})
get-library-docs_Context_7({LIBRARY_ID})
```

### 第二步：数据分析
```bash
# 使用sequentialthinking分析整理
sequentialthinking_Sequential_thinking(
    分析收集到的信息
    构建思维导图结构
    确保内容完整性
)
```

### 第三步：HTML生成
```bash
# 创建交互式HTML文件
save-file(docs/{library_name}_complete_mindmap.html)
```

### 第四步：验证
```bash
# 在浏览器中打开验证
open-browser(file:///path/to/{library_name}_complete_mindmap.html)
```

---

## ✅ 验收标准

### 功能验收
- [ ] 搜索功能正常工作
- [ ] 折叠/展开功能正常
- [ ] 代码示例可以正确显示
- [ ] 响应式设计在不同设备上正常

### 内容验收
- [ ] 覆盖了`{LIBRARY_NAME}`的所有主要功能
- [ ] 每个API都有清晰的描述
- [ ] 代码示例准确可用
- [ ] 结构层次清晰合理

### 质量验收
- [ ] 信息来源权威（deepwiki + context7）
- [ ] 内容组织逻辑清晰
- [ ] 视觉设计美观易用
- [ ] 加载速度快，交互流畅

---

## 📝 使用说明

### 如何发起请求
只需提供：
1. **库名称**: 如 "FastAPI"、"Pandas"、"Selenium" 等
2. **特殊关注点**: 如果有特定的功能模块需要重点关注

### 示例请求
```
请为 FastAPI 创建详细的学习思维导图，重点关注路由和依赖注入功能。
```

### 预期交付
- 完整的HTML思维导图文件
- 在浏览器中的演示
- 简要的使用说明

---

## 🔄 迭代改进

如需要调整或补充：
- 可以要求增加特定模块的详细信息
- 可以要求调整视觉样式
- 可以要求添加更多代码示例
- 可以要求重新组织结构层次

---

*最后更新: 2025-01-30*
*适用于: 库/框架学习思维导图创建*
